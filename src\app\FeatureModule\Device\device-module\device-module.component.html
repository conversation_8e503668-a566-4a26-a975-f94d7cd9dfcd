<!-- loading start -->
<div class="ringLoading" *ngIf="loading">
    <!-- loading gif start -->
    <div class="ringLoadingDiv">
        <img src="assets/DoubleRing.gif" alt="loading" *ngIf="loading">
    </div>
    <!-- loading gif end -->
</div>
<!-- loading end -->

<!------------------------------------------------------------->
<!----------- Device Listing Page Start ------------------------->
<!------------------------------------------------------------->
<ng-template [ngIf]="isDeviceListingPageDisplay">
    <app-device-list (deviceId)="getDeviceId($event)" [deviceSearchRequestBody]="deviceSearchRequestBody"
        (deviceSearchRequestBodyChange)="updateDeviceSearchRequestBody($event)" [isFilterHidden]="isFilterHidden"
        (isFilterHiddenChange)="updateIsFilterHidden($event)" (showDeviceDetail)="showDeviceDetail()"></app-device-list>
</ng-template>
<!------------------------------------------------------------>
<!--------- Device Listing Page  End-------------------------->
<!------------------------------------------------------------>

<!------------------------------------------------------------->
<!----------- Tranfer Order Selection ------------------------->
<!------------------------------------------------------------->
<ng-template [ngIf]="isDeviceDetailPageDisplay">
    <app-device-detail [deviceIdInput]="deviceIdInput" [resource]="deviceListResource" (showDevice)="showDevice()">
    </app-device-detail>
</ng-template>
<!------------------------------------------------------------>
<!--------- Tranfer Order Selection End----------------------->
<!------------------------------------------------------------->