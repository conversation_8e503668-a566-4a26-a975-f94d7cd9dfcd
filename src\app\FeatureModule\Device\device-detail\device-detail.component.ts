import { Component, EventEmitter, Input, OnInit, Output } from '@angular/core';
import { ToastrService } from 'ngx-toastr';
import { Subscription } from 'rxjs';
import { ASSIGN_RELEASE_VERSION_BTN_TEXT, BACK_BTN_TEXT, Cancel, DEVICE_APP_VERSION, DEVICE_CONNECTION_STATE, DEVICE_COUNTRY, DEVICE_CREATE_DATE_AND_TIME, DEVICE_CUSTOMER_EMAIL, DEVICE_CUSTOMER_NAME, DEVICE_DELETE, DEVICE_EDITABLE, DEVICE_HANDLE_VERSION, DEVICE_HAS_NO_SERIAL_NUMBER, DEVICE_HW_ID, DEVICE_JSON_VERSION, DEVICE_LOCKED, DEVICE_MAC_ADDRESS, DEVICE_MODIFY_DATE_AND_TIME, DEVICE_ORDER_RECORD_TYPE, DEVICE_PART_NO, DEVICE_PO_NO, DEVICE_PROBE_VERSION, DEVICE_RELEASE_VERSION, DEVICE_SALES_ORDER_NUMBER, DEVICE_SERIAL_NO, DEVICE_SETTING_VERSION, DEVICE_STATUS, DEVICE_STATUS_NOT_ENABLE, DEVICE_SYSTEM_SW_VERSION, DEVICE_TIME_ZONE, DEVICE_TYPE, DEVICE_UPS_VERSION, DeviceDetailResource, ITEMS_PER_PAGE, LAST_CHECK_IN_DATE_AND_TIME, PIMS_VERSION, SALES_ORDER_PARTIALLY_CONFIGURED, SalesOrderAssociationHeader, Submit, TRANSFER_ORDER } from '../../../app.constants';
import { ConfirmDialogService } from '../../../confirmationdialog/confirmation.service';
import { CountryListResponse } from '../../../model/Country/CountryListResponse.model';
import { BooleanKeyValueMapping } from '../../../model/common/BooleanKeyValueMapping.model';
import { EnumMapping } from '../../../model/common/EnumMapping.model';
import { CustomerAssociationModelRequest } from '../../../model/customer-association-model/customer-association-model-request.model';
import { TransferProductDetails } from '../../../model/device/TransferProductDetails.model';
import { DeviceDetailResponse } from '../../../model/device/deviceDetailResponse.model';
import { CountryCacheService } from '../../../shared/Service/CacheService/countrycache.service';
import { DeviceService } from '../../../shared/device.service';
import { ProductStatusEnum } from '../../../shared/enum/Common/ProductStatus.enum';

import { PermissionAction } from '../../../shared/enum/Permission/permissionAction.enum';
import { ProductConfigStatus } from '../../../shared/enum/SalesOrder/ProductConfigStatus.enum';
import { deviceTypesEnum } from '../../../shared/enum/deviceTypesEnum.enum';
import { CustomerAssociationService } from '../../../shared/modalservice/customer-association.service';
import { PermissionService } from '../../../shared/permission.service';
import { EnumMappingDisplayNamePipe } from '../../../shared/pipes/Common/EnumMappingDisplayNamePipe.pipe';
import { CommonOperationsService } from '../../../shared/util/common-operations.service';
import { KeyValueMappingServiceService } from '../../../shared/util/key-value-mapping-service.service';
import { ValidationService } from '../../../shared/util/validation.service';
import { DeviceOperationService } from '../DeviceService/Device-Operation/device-operation.service';
import { ListingPageReloadSubjectParameter } from '../../../model/common/listingPageReloadSubjectParameter.model';

@Component({
  selector: 'app-device-detail',
  templateUrl: './device-detail.component.html',
  styleUrls: ['./device-detail.component.css']
})
export class DeviceDetailComponent implements OnInit {
  @Input("deviceIdInput") deviceIdInput: any;
  @Input("resource") resource: string;
  @Output("showDevice") showDevice = new EventEmitter;
  countryList: Array<CountryListResponse> = [];

  deviceDetailResponse: DeviceDetailResponse = null;

  loading: boolean = false;
  releaseVersions: ReleaseVersions[] = [];
  releaseVersionId: number;
  selectedReleaseVersion: number = -1;
  btnReleaseVersionDisable: boolean = true;

  //Page Dispaly
  deviceDetailDisplay: boolean = false;
  transferOrderSelectionDisaplay: boolean = false;

  subscriptionForLoading: Subscription;
  subscriptionForDeviceDetailRefresh: Subscription;
  subscriptionForTransferDeviceUI: Subscription;

  itemsPerPage = ITEMS_PER_PAGE;
  page = 0;

  deviceOperations: string[] = [];

  //Permission
  updateDeviceTypePermission: boolean = false;

  //Option List
  productStatusList: Array<EnumMapping> = [];
  lockUnlockStatus: Array<BooleanKeyValueMapping> = [];
  editEnableDisableStatus: Array<BooleanKeyValueMapping> = [];
  deviceDetailsTrasferProduct: TransferProductDetails = null;

  backBtnText: string = BACK_BTN_TEXT;
  serialNo: string = DEVICE_SERIAL_NO;
  hwId: string = DEVICE_HW_ID;
  salesOrderNumber: string = DEVICE_SALES_ORDER_NUMBER;
  deviceType: string = DEVICE_TYPE;
  country: string = DEVICE_COUNTRY;
  systemSwVerstion: string = DEVICE_SYSTEM_SW_VERSION;
  connectionState: string = DEVICE_CONNECTION_STATE;
  customerName: string = DEVICE_CUSTOMER_NAME;
  locked: string = DEVICE_LOCKED;
  editable: string = DEVICE_EDITABLE;
  status: string = DEVICE_STATUS;
  lastCheckinDataAndType: string = LAST_CHECK_IN_DATE_AND_TIME;
  macAddress: string = DEVICE_MAC_ADDRESS;
  createDateAndTime: string = DEVICE_CREATE_DATE_AND_TIME;
  modifyDateAndTime: string = DEVICE_MODIFY_DATE_AND_TIME;
  timeZone: string = DEVICE_TIME_ZONE;
  probeVersion: string = DEVICE_PROBE_VERSION;
  handleVersion: string = DEVICE_HANDLE_VERSION;
  pimsVersion: string = PIMS_VERSION;
  settingVersion: string = DEVICE_SETTING_VERSION;
  upsVersion: string = DEVICE_UPS_VERSION;
  appVersion: string = DEVICE_APP_VERSION;
  jsonVersion: string = DEVICE_JSON_VERSION;
  orderRecordType: string = DEVICE_ORDER_RECORD_TYPE;
  partNo: string = DEVICE_PART_NO;
  po_no: string = DEVICE_PO_NO;
  customerEmail: string = DEVICE_CUSTOMER_EMAIL;
  releaseVersion: string = DEVICE_RELEASE_VERSION;
  assignReleaseVersionBtnText: string = ASSIGN_RELEASE_VERSION_BTN_TEXT;


  constructor(protected deviceService: DeviceService,
    private toster: ToastrService,
    private customerAssociationService: CustomerAssociationService,
    private permissionService: PermissionService,
    private commonOperationsService: CommonOperationsService,
    private keyValueMappingServiceService: KeyValueMappingServiceService,
    private toste: ToastrService,
    private confirmDialogService: ConfirmDialogService,
    private enumMappingDisplayNamePipe: EnumMappingDisplayNamePipe,
    private validationService: ValidationService,
    private countryCacheService: CountryCacheService,
    private deviceOperationService: DeviceOperationService) { }

  public async ngOnInit(): Promise<void> {
    this.loading = true;
    this.countryList = await this.countryCacheService.getCountryListFromCache(false);
    this.updateDeviceTypePermission = this.permissionService.getDevicePermission(PermissionAction.UPDATE_DEVICE_TYPE_ACTION);
    //Get Product Status Option list
    this.productStatusList = this.keyValueMappingServiceService.enumOptionToList(ProductStatusEnum);
    //Get Locked/Unlocked Option list
    this.lockUnlockStatus = this.keyValueMappingServiceService.lockedUnlockOptionList();
    this.editEnableDisableStatus = this.keyValueMappingServiceService.editEnableDisableOptionList();
    this.deviceDetailDisplay = true;
    this.deviceDetailModel(this.deviceIdInput);
    this.subjectInit();
  }

  ngOnDestroy() {
    if (this.subscriptionForLoading != undefined) {
      this.subscriptionForLoading.unsubscribe();
    }
    if (this.subscriptionForDeviceDetailRefresh != undefined) {
      this.subscriptionForDeviceDetailRefresh.unsubscribe();
    }
    if (this.subscriptionForTransferDeviceUI != undefined) {
      this.subscriptionForTransferDeviceUI.unsubscribe();
    }
  }

  private subjectInit(): void {
    // Loading subscription
    this.subscriptionForLoading = this.deviceOperationService.getDeviceDetailLoadingSubject()?.subscribe((status: boolean) => {
      this.loading = status;
    });

    // Refresh subscription
    this.subscriptionForDeviceDetailRefresh = this.deviceOperationService.getDeviceDetailRefreshSubject()?.subscribe((listingPageReloadSubjectParameter: ListingPageReloadSubjectParameter) => {
      if (listingPageReloadSubjectParameter.isReloadData) {
        this.deviceDetailModel(this.deviceIdInput);
      }
    });

    // Transfer Device UI subscription
    this.subscriptionForTransferDeviceUI = this.deviceOperationService.getTransferDeviceUISubject()?.subscribe((showTransferUI: boolean) => {
      if (showTransferUI) {
        this.transferOrderSelectionToggle(false, true);
      }
    });
  }

  async deviceDetailModel(deviceId: number) {
    this.loading = true;

    try {
      const result = await this.deviceOperationService.loadDeviceDetail(deviceId);

      if (result.success) {
        this.deviceDetailResponse = result.deviceDetail;
        this.releaseVersionId = result.releaseVersionId;
        this.deviceDetailsTrasferProduct = result.transferProductDetails;

        // Get release versions
        await this.getReleaseVersions(
          this.deviceDetailResponse?.deviceType,
          this.deviceDetailResponse?.countryId,
          this.deviceDetailResponse?.packageVersion
        );

        // Set device operations
        const transferProduct: boolean = this.deviceDetailResponse?.orderRecordType === TRANSFER_ORDER;
        this.deviceOperations = this.commonOperationsService.accessDeviceListOperations(false, transferProduct, this.resource);
      } else {
        this.toster.warning(DEVICE_DELETE);
        this.back();
      }
    } finally {
      this.loading = false;
    }
  }

  private async getReleaseVersions(deviceType: deviceTypesEnum, countryId: number, packageVersion: string): Promise<void> {
    try {
      const result = await this.deviceOperationService.getReleaseVersions(
        deviceType,
        countryId,
        packageVersion,
        this.updateDeviceTypePermission
      );

      if (result.success) {
        this.releaseVersions = result.releaseVersions;
        this.selectedReleaseVersion = JSON.parse(JSON.stringify(this.releaseVersionId));
        this.btnReleaseVersionDisable = this.deviceOperationService.shouldDisableAssignButton(
          this.selectedReleaseVersion,
          this.releaseVersionId
        );
      } else {
        this.setEmptyReleaseVersions();
      }
    } catch (error) {
      this.setEmptyReleaseVersions();
    }
  }

  private setEmptyReleaseVersions(): void {
    this.releaseVersions = [];
    this.selectedReleaseVersion = -1;
    this.toggleAssignButton();
  }

  public changeReleaseVersion(event: any): void {
    const changedValue = event.target.value;
    const item = this.releaseVersions.filter(releaseVersion => releaseVersion.id == changedValue);

    if (item.length == 1) {
      this.selectedReleaseVersion = item[0].id;
    } else {
      this.selectedReleaseVersion = -1;
    }

    this.btnReleaseVersionDisable = this.deviceOperationService.shouldDisableAssignButton(
      this.selectedReleaseVersion,
      this.releaseVersionId
    );
  }

  public async assignReleaseVersion(): Promise<void> {
    if (!this.deviceOperationService.validateSingleDevicePermissions(this.deviceDetailResponse, DeviceDetailResource)) {
      return;
    }

    this.loading = true;
    try {
      await this.deviceOperationService.assignReleaseVersion(this.deviceDetailResponse.id, this.selectedReleaseVersion);
      await this.deviceDetailModel(this.deviceIdInput);
    } finally {
      this.loading = false;
    }
  }

  private toggleAssignButton(): void {
    this.btnReleaseVersionDisable = this.deviceOperationService.shouldDisableAssignButton(
      this.selectedReleaseVersion,
      this.releaseVersionId
    );
  }

  back() {
    this.showDevice.emit();
  }

  /**
  * Device Operations list
  * Uses centralized operation handler from common operations service
  * <AUTHOR>
  */
  public changeDeviceOperation(event: any): void {
    this.commonOperationsService.changeOperationForDevice(event.target.value, DeviceDetailResource, [this.deviceDetailResponse.id], false, [this.deviceDetailResponse]);
  }

  /**
  * Association or Update customer email
  *
  * <AUTHOR>
  */
  public async associationDeviceWithSalesOrder(): Promise<void> {
    const confirmedRequestBody = await this.customerAssociationService.openCustomerAssociationPopup(
      new CustomerAssociationModelRequest(DeviceDetailResource, SalesOrderAssociationHeader, Submit, Cancel, this.deviceDetailResponse.salesOrderNumber)
    ).catch(() => {
      // User dismissed the dialog (ESC key or click outside)
      return { button: false, basicSalesOrderDetailResponse: null, isSalesOrderNewAdd: false };
    });

    if (confirmedRequestBody.button) {
      this.loading = true;
      try {
        const success = await this.deviceOperationService.associateDevicesWithSalesOrder([this.deviceDetailResponse.id], [this.deviceDetailResponse], confirmedRequestBody.basicSalesOrderDetailResponse, confirmedRequestBody.isSalesOrderNewAdd, DeviceDetailResource);
        if (success) {
          await this.ngOnInit();
        }
      } finally {
        this.loading = false;
      }
    }
  }

  /**
  * Validate Product Status For Disable Action
  *
  * <AUTHOR>
  */
  public async validateProductStatusForDisableAction(): Promise<void> {
    await this.disableProductStatusForDevice();
  }

  /**
  * Editable Enable And Disable
  *
  * <AUTHOR>
  */
  public async enableDisableDevice(editable: boolean): Promise<void> {
    this.loading = true;
    try {
      const success = await this.deviceOperationService.enableDisableDevices([this.deviceDetailResponse.id], [this.deviceDetailResponse], editable, DeviceDetailResource);
      if (success) {
        await this.deviceDetailModel(this.deviceIdInput);
      }
    } finally {
      this.loading = false;
    }
  }

  /**
  *  Locked And unlocked
  *
  * <AUTHOR>
  */
  public async lockUnlock(lockState: boolean): Promise<void> {
    this.loading = true;
    try {
      const success = await this.deviceOperationService.lockUnlockDevices([this.deviceDetailResponse.id], [this.deviceDetailResponse], lockState, DeviceDetailResource);
      if (success) {
        await this.deviceDetailModel(this.deviceIdInput);
      }
    } finally {
      this.loading = false;
    }
  }

  /**
  * Convert Device Type as Test
  *
  * <AUTHOR>
  */
  public async convertDataToTest(): Promise<void> {
    this.loading = true;
    try {
      const success = await this.deviceOperationService.convertDevicesToTest([this.deviceDetailResponse.id], [this.deviceDetailResponse], DeviceDetailResource);
      if (success) {
        await this.deviceDetailModel(this.deviceIdInput);
      }
    } finally {
      this.loading = false;
    }
  }

  /**
  * transfer Device
  *
  * <AUTHOR>
  */
  public async transferDevice(): Promise<void> {
    this.loading = true;
    try {
      await this.deviceOperationService.transferDevices([this.deviceDetailResponse.id], [this.deviceDetailResponse], DeviceDetailResource);
    } finally {
      this.loading = false;
    }
  }

  /**
  * Convert Device Type as Demo
  *
  * <AUTHOR>
  */
  public async convertDataToDemo(): Promise<void> {
    this.loading = true;
    try {
      const success = await this.deviceOperationService.convertDevicesToDemo([this.deviceDetailResponse.id], [this.deviceDetailResponse], DeviceDetailResource);
      if (success) {
        await this.deviceDetailModel(this.deviceIdInput);
      }
    } finally {
      this.loading = false;
    }
  }

  /**
  * Convert Device Type as Client
  *
  * <AUTHOR>
  */
  public async convertDataToClient(): Promise<void> {
    this.loading = true;
    try {
      const success = await this.deviceOperationService.convertDevicesToClient([this.deviceDetailResponse.id], [this.deviceDetailResponse], DeviceDetailResource);
      if (success) {
        await this.deviceDetailModel(this.deviceIdInput);
      }
    } finally {
      this.loading = false;
    }
  }

  /**
  * Validate Product Status For RMA Action
  *
  * <AUTHOR>
  */
  public async validateProductStatusForRMAAction(): Promise<void> {
    await this.rmaProductStatusForDevice();
  }

  /**
  * Disable Product Status For Device
  *
  * <AUTHOR>
  */
  private async disableProductStatusForDevice(): Promise<void> {
    const basicModelConfig = this.confirmDialogService.getBasicModelConfigForDisableAction(DeviceDetailResource);

    const confirmed = await this.confirmDialogService.confirm(basicModelConfig.title, basicModelConfig.message, basicModelConfig.btnOkText, basicModelConfig.btnCancelText).catch(() => {
      // User dismissed the dialog (ESC key or click outside)
      this.loading = false;
      return false;
    });

    if (confirmed) {
      this.loading = true;
      try {
        const success = await this.deviceOperationService.disableProductStatusForDevices([this.deviceDetailResponse.id], [this.deviceDetailResponse], DeviceDetailResource);
        if (success) {
          await this.deviceDetailModel(this.deviceIdInput);
        }
      } finally {
        this.loading = false;
      }
    }
  }

  /**
  * RMA Product Status For Device
  *
  * <AUTHOR>
  */
  private async rmaProductStatusForDevice(): Promise<void> {
    const basicModelConfig = this.confirmDialogService.getBasicModelConfigForRMAAction(DeviceDetailResource);

    const confirmed = await this.confirmDialogService.confirm(
      basicModelConfig.title,
      basicModelConfig.message,
      basicModelConfig.btnOkText,
      basicModelConfig.btnCancelText
    ).catch(() => {
      // User dismissed the dialog (ESC key or click outside)
      this.loading = false;
      return false;
    });

    if (confirmed) {
      this.loading = true;
      try {
        const success = await this.deviceOperationService.rmaProductStatusForDevices([this.deviceDetailResponse.id], [this.deviceDetailResponse], DeviceDetailResource, this.productStatusList, this.enumMappingDisplayNamePipe, this.validationService, this.confirmDialogService);
        if (success) {
          await this.deviceDetailModel(this.deviceIdInput);
        }
      } finally {
        this.loading = false;
      }
    }
  }

  /**
  * Toggles the display states for device details and transfer order sections.
  * 
  * <AUTHOR>
  * 
  * @param deviceDetailDisplay 
  * @param tranferOrderSectionDisplay
  */
  public transferOrderSelectionToggle(deviceDetailDisplay: boolean, tranferOrderSectionDisplay: boolean) {
    this.transferOrderSelectionDisaplay = tranferOrderSectionDisplay;
    this.deviceDetailDisplay = deviceDetailDisplay;
    if (deviceDetailDisplay) {
      this.deviceDetailModel(this.deviceIdInput);
    }
  }

  /**
  * Refresh Device Detail Page Data
  * 
  * <AUTHOR> 
  */
  public refreshDeviceDetailPage(): void {
    this.deviceDetailModel(this.deviceIdInput);
  }
}

export class ReleaseVersions {
  public id: number;
  public itemNumber: string;
}
